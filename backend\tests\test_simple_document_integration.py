# backend/tests/test_simple_document_integration.py
"""
Simple integration test for Document entity functionality.

This test verifies that the Document entity implementation works end-to-end
without complex API testing.
"""

import pytest
from sqlalchemy.orm import Session

from core.models.documents import CalculationStandard
from core.repositories.document_repository import CalculationStandardRepository
from core.services.document_service import DocumentService
from core.repositories.project_repository import ProjectRepository
from core.repositories.user_repository import UserRepository
from core.schemas.document_schemas import (
    CalculationStandardCreateSchema,
    CalculationStandardReadSchema,
)


class TestDocumentIntegration:
    """Test Document entity integration."""

    def test_calculation_standard_repository_crud(self, db_session: Session):
        """Test basic CRUD operations for CalculationStandard repository."""
        repository = CalculationStandardRepository(db_session)
        
        # Create
        standard_data = {
            "name": "Test Standard",
            "standard_code": "TEST-001",
            "description": "Test description",
            "parameters_json": '{"test": "value"}',
        }
        
        standard = repository.create(standard_data)
        db_session.commit()
        
        assert standard.id is not None
        assert standard.name == "Test Standard"
        assert standard.standard_code == "TEST-001"
        
        # Read
        found_standard = repository.get_by_id(standard.id)
        assert found_standard is not None
        assert found_standard.name == "Test Standard"
        
        # Update
        found_standard.description = "Updated description"
        db_session.commit()
        
        updated_standard = repository.get_by_id(standard.id)
        assert updated_standard.description == "Updated description"
        
        # Delete (soft delete)
        updated_standard.is_deleted = True
        db_session.commit()
        
        # Verify it's marked as deleted
        deleted_standard = repository.get_by_id(standard.id)
        assert deleted_standard.is_deleted is True

    def test_calculation_standard_service_operations(self, db_session: Session):
        """Test CalculationStandard service operations."""
        # Setup repositories
        calc_repo = CalculationStandardRepository(db_session)
        project_repo = ProjectRepository(db_session)
        user_repo = UserRepository(db_session)
        
        # Create service
        service = DocumentService(
            imported_data_revision_repository=None,  # Not needed for this test
            exported_document_repository=None,  # Not needed for this test
            calculation_standard_repository=calc_repo,
            project_repository=project_repo,
            user_repository=user_repo,
        )
        
        # Test create
        create_data = CalculationStandardCreateSchema(
            name="Service Test Standard",
            standard_code="STS-001",
            description="Service test description",
            parameters_json='{"service": "test"}',
        )
        
        created_standard = service.create_calculation_standard(create_data)
        
        assert isinstance(created_standard, CalculationStandardReadSchema)
        assert created_standard.name == "Service Test Standard"
        assert created_standard.standard_code == "STS-001"
        assert created_standard.id is not None
        
        # Test get by ID
        retrieved_standard = service.get_calculation_standard_by_id(created_standard.id)
        assert retrieved_standard.name == "Service Test Standard"
        
        # Test get by code
        retrieved_by_code = service.get_calculation_standard_by_code("STS-001")
        assert retrieved_by_code.name == "Service Test Standard"
        
        # Test list all
        standards, total = service.get_all_calculation_standards()
        assert total >= 1
        assert any(s.id == created_standard.id for s in standards)
        
        # Test delete
        success = service.delete_calculation_standard(created_standard.id)
        assert success is True
        
        # Verify it's deleted (should raise NotFoundError)
        with pytest.raises(Exception):  # Should be NotFoundError but we'll catch any exception
            service.get_calculation_standard_by_id(created_standard.id)

    def test_calculation_standard_schema_validation(self):
        """Test CalculationStandard schema validation."""
        # Test valid data
        valid_data = {
            "name": "Valid Standard",
            "standard_code": "VALID-001",
            "description": "Valid description",
            "parameters_json": '{"valid": "json"}',
        }
        
        schema = CalculationStandardCreateSchema(**valid_data)
        assert schema.name == "Valid Standard"
        assert schema.standard_code == "VALID-001"
        
        # Test invalid JSON
        with pytest.raises(Exception):  # Should be ValidationError
            CalculationStandardCreateSchema(
                name="Invalid Standard",
                standard_code="INVALID-001",
                parameters_json="invalid json",
            )
        
        # Test empty name
        with pytest.raises(Exception):  # Should be ValidationError
            CalculationStandardCreateSchema(
                name="",
                standard_code="EMPTY-001",
            )

    def test_database_schema_creation(self, db_session: Session):
        """Test that document tables are created correctly."""
        # This test verifies that the database schema includes our document tables
        # by attempting to create a record directly
        
        standard = CalculationStandard(
            name="Direct DB Test",
            standard_code="DB-001",
            description="Direct database test",
        )
        
        db_session.add(standard)
        db_session.commit()
        db_session.refresh(standard)
        
        assert standard.id is not None
        assert standard.name == "Direct DB Test"
        assert standard.created_at is not None
        assert standard.is_deleted is False

    def test_document_models_relationships(self, db_session: Session, sample_project, sample_user):
        """Test that document model relationships work correctly."""
        from core.models.documents import ImportedDataRevision, ExportedDocument
        
        # Test ImportedDataRevision relationship with Project and User
        revision = ImportedDataRevision(
            project_id=sample_project.id,
            imported_by_user_id=sample_user.id,
            source_filename="test.xlsx",
            import_type="pipe_data",
            is_active_revision=True,
        )
        
        db_session.add(revision)
        db_session.commit()
        db_session.refresh(revision)
        
        assert revision.project_id == sample_project.id
        assert revision.imported_by_user_id == sample_user.id
        
        # Test ExportedDocument relationship with Project and User
        document = ExportedDocument(
            project_id=sample_project.id,
            generated_by_user_id=sample_user.id,
            document_type="heat_tracing_report",
            filename="test_report.pdf",
            is_latest_revision=True,
        )
        
        db_session.add(document)
        db_session.commit()
        db_session.refresh(document)
        
        assert document.project_id == sample_project.id
        assert document.generated_by_user_id == sample_user.id
